"""
Teste das melhorias implementadas no Prospector
"""
import sys
import os

# Adicionar o diretório atual ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logic_bot import gerar_termos_relacionados

def test_gerar_termos_relacionados():
    """Testa a função de geração de termos relacionados"""

    print("=== TESTE: Geração de Termos Relacionados EXPANDIDOS ===\n")

    # Lista de testes expandida
    testes = [
        "PSICOLOGIA",
        "medicina",
        "dentista",
        "restaurante",
        "advogado",
        "contador",
        "engenheiro",
        "arquiteto",
        "fisioterapeuta",
        "nutricionista",
        "veterinário",
        "estética",
        "cabeleireiro",
        "loja",
        "informática",
        "construção",
        "escola",
        "farmácia",
        "oficina",
        "hotel",
        "xyz"  # Termo genérico
    ]

    for i, termo in enumerate(testes, 1):
        termos_relacionados = gerar_termos_relacionados(termo)
        print(f"{i:2d}. Termo: {termo}")
        print(f"    Termos relacionados ({len(termos_relacionados)}): {termos_relacionados}")
        print()

    # Estatísticas
    total_termos = 0
    for termo in testes[:-1]:  # Excluir o termo genérico
        termos_relacionados = gerar_termos_relacionados(termo)
        total_termos += len(termos_relacionados)

    print(f"📊 ESTATÍSTICAS:")
    print(f"   • Total de categorias testadas: {len(testes)-1}")
    print(f"   • Média de termos por categoria: {total_termos/(len(testes)-1):.1f}")
    print(f"   • Total de termos relacionados disponíveis: {total_termos}")
    print()

def test_melhorias_resumo():
    """Mostra um resumo das melhorias implementadas"""

    print("=== RESUMO DAS MELHORIAS IMPLEMENTADAS ===\n")

    melhorias = [
        "🚀 1. Sistema de Termos Relacionados MASSIVAMENTE EXPANDIDO:",
        "   - 🎯 35+ categorias profissionais e comerciais",
        "   - 📈 Mais de 400 termos relacionados disponíveis",
        "   - 🔍 Categorias incluem: Saúde, Estética, Jurídico, Tecnologia, Alimentação,",
        "     Varejo, Automóveis, Imóveis, Eventos, Consultoria, Pets, Agro, Turismo, etc.",
        "   - 🎲 Fallback inteligente para termos genéricos",
        "   - 📊 Até 10 termos relacionados por busca (aumentado de 5)",
        "",
        "🔄 2. Busca Expandida Automática:",
        "   - ⚡ Ativação automática quando não atinge quantidade desejada",
        "   - 🗺️ Mantém localização, varia termos de busca",
        "   - ⚙️ Processa até 10 resultados por termo relacionado",
        "   - 🎯 Estratégia inteligente de diversificação",
        "",
        "🛠️ 3. Funções Auxiliares Melhoradas:",
        "   - 📝 extrair_nome_cliente(): Múltiplas estratégias CSS e XPath",
        "   - 📞 extrair_telefone_cliente(): Detecção avançada com regex",
        "   - 📍 extrair_localizacao_da_busca(): Extração automática de localização",
        "   - 🔄 extrair_clientes_termo_relacionado(): Extração otimizada",
        "",
        "🚫 4. Prevenção de Duplicatas Avançada:",
        "   - 🔒 Controle global de clientes únicos",
        "   - ✅ Verificação antes de cada adição",
        "   - 🎯 Eficiência máxima sem reprocessamento",
        "",
        "📊 5. Logs e Monitoramento Aprimorados:",
        "   - 📈 Rastreamento detalhado de progresso",
        "   - 🔍 Indicação clara de uso de termos relacionados",
        "   - ⚠️ Melhor tratamento e logging de erros",
        "   - 📋 Estatísticas de performance por termo"
    ]

    for melhoria in melhorias:
        print(melhoria)

    print("\n=== COMO AS MELHORIAS RESOLVEM OS PROBLEMAS ===\n")

    solucoes = [
        "❌ PROBLEMA: Apenas 5 leads encontrados de 300 solicitados (1.67% eficiência)",
        "✅ SOLUÇÃO: Sistema com 400+ termos relacionados expande automaticamente",
        "   📈 IMPACTO: Estimativa de 100-500 leads (20-100x mais resultados)",
        "",
        "❌ PROBLEMA: Busca muito específica (apenas 'PSICOLOGIA')",
        "✅ SOLUÇÃO: Para 'PSICOLOGIA' agora gera automaticamente:",
        "   • psicólogo, psicóloga, terapia, psicoterapia, clínica psicológica",
        "   • consultório psicológico, saúde mental, terapeuta, psicoterapeuta",
        "   • psicologia clínica, psicologia infantil, neuropsicologia, etc.",
        "",
        "❌ PROBLEMA: Limitação geográfica restrita",
        "✅ SOLUÇÃO: Mantém 'Campo Grande' mas testa múltiplos termos:",
        "   🎯 'psicólogo Campo Grande', 'terapia Campo Grande', etc.",
        "",
        "❌ PROBLEMA: Dados incompletos (só 4 de 5 com telefone)",
        "✅ SOLUÇÃO: Funções melhoradas com múltiplas estratégias de extração",
        "   📞 Melhor detecção de telefones e dados de contato",
        "",
        "🚀 RESULTADO ESPERADO PARA 'PSICOLOGIA' EM 'CAMPO GRANDE':",
        "   • ANTES: 5 leads únicos",
        "   • DEPOIS: 50-200 leads únicos (10-40x mais resultados)",
        "   • DIVERSIDADE: 10+ termos relacionados testados automaticamente",
        "   • QUALIDADE: Melhor taxa de extração de dados de contato"
    ]

    for solucao in solucoes:
        print(solucao)

if __name__ == "__main__":
    test_gerar_termos_relacionados()
    print("\n" + "="*60 + "\n")
    test_melhorias_resumo()
